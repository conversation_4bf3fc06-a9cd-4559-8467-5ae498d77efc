'use client';

import { useState, useEffect } from 'react';
import { Menu, X } from 'lucide-react';
import gsapUtils from '../../lib/gsap-utils';
import ThemeToggle from '../ui/ThemeToggle';

const SinglePageNavbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  const navItems = [
    { name: 'Home', href: '#home' },
    { name: 'About', href: '#about' },
    { name: 'Programs', href: '#programs' },
    { name: 'Stats', href: '#stats' },
    { name: 'Team', href: '#team' },
    { name: 'Contact', href: '#contact' },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    
    // GSAP animation for navbar entrance
    gsapUtils.fadeIn('.navbar-content', { delay: 0.2 });

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleNavClick = (href) => {
    setIsOpen(false);
    if (href.startsWith('#')) {
      const element = document.querySelector(href);
      if (element) {
        const navbarHeight = 80;
        const elementPosition = element.offsetTop - navbarHeight;
        window.scrollTo({
          top: elementPosition,
          behavior: 'smooth'
        });
      }
    }
  };

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-background/90 backdrop-blur-md border-b border-border/50 shadow-lg'
        : 'bg-transparent'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="navbar-content flex items-center justify-between h-16">
          <a href="#home" onClick={() => handleNavClick('#home')} className="flex items-center group">
            <div className="flex-shrink-0 flex items-center">
              <div className="w-10 h-10 bg-gradient-to-br from-electric-blue to-bright-orange rounded-full flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <span className="text-white font-bold text-lg font-arvo">NG</span>
              </div>
              <span className="text-foreground font-arvo font-bold text-xl group-hover:text-primary transition-colors duration-300">
                NextGen Youth Movement
              </span>
            </div>
          </a>

          <div className="hidden md:flex items-center space-x-8">
            <div className="flex items-baseline space-x-6">
              {navItems.map((item, index) => (
                <a
                  key={item.name}
                  href={item.href}
                  onClick={(e) => {
                    e.preventDefault();
                    handleNavClick(item.href);
                  }}
                  className="text-muted-foreground hover:text-foreground px-3 py-2 text-sm font-medium font-arvo transition-all duration-300 relative group cursor-pointer"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {item.name}
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-electric-blue to-bright-orange transition-all duration-300 group-hover:w-full"></span>
                </a>
              ))}
            </div>

            <ThemeToggle />

            <a
              href="#contact"
              onClick={(e) => {
                e.preventDefault();
                handleNavClick('#contact');
              }}
              className="inline-flex items-center px-6 py-2 bg-gradient-to-r from-electric-blue to-bright-orange text-white font-semibold rounded-lg hover:scale-105 hover:shadow-lg hover:shadow-primary/30 transition-all duration-300 text-sm font-arvo cursor-pointer"
            >
              Join the Movement
            </a>
          </div>

          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-muted-foreground hover:text-foreground focus:outline-none focus:text-foreground transition-colors duration-300"
            >
              {isOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>
      </div>

      <div className={`md:hidden fixed inset-y-0 right-0 z-50 w-64 bg-background/95 backdrop-blur-md border-l border-border/50 shadow-xl transform transition-transform duration-300 ease-in-out ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between p-4 border-b border-border/50">
            <span className="text-foreground font-arvo font-bold text-lg">Menu</span>
            <button
              onClick={() => setIsOpen(false)}
              className="text-muted-foreground hover:text-foreground transition-colors duration-300"
            >
              <X size={24} />
            </button>
          </div>

          <div className="flex-1 px-4 py-6 space-y-4">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                onClick={(e) => {
                  e.preventDefault();
                  handleNavClick(item.href);
                }}
                className="block text-muted-foreground hover:text-foreground px-3 py-3 text-base font-medium font-arvo transition-colors duration-300 cursor-pointer rounded-lg hover:bg-muted/50"
              >
                {item.name}
              </a>
            ))}
          </div>

          <div className="p-4 border-t border-border/50 space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-foreground font-arvo">Theme</span>
              <ThemeToggle />
            </div>
            
            <a
              href="#contact"
              onClick={(e) => {
                e.preventDefault();
                handleNavClick('#contact');
              }}
              className="block w-full text-center px-6 py-3 bg-gradient-to-r from-electric-blue to-bright-orange text-white font-semibold rounded-lg hover:scale-105 transition-transform duration-300 font-arvo cursor-pointer"
            >
              Join the Movement
            </a>
          </div>
        </div>
      </div>

      {isOpen && (
        <div 
          className="md:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </nav>
  );
};

export default SinglePageNavbar;

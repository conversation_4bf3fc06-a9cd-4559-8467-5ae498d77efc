#!/bin/bash

# Replace Facebook icon
sed -i 's/<span className="text-white font-bold text-sm">f<\/span>/<Facebook className="w-5 h-5 text-white" \/>/' components/layout/EnhancedFooter.tsx

# Replace Instagram icon
sed -i 's/<span className="text-white font-bold text-sm">ig<\/span>/<Instagram className="w-5 h-5 text-white" \/>/' components/layout/EnhancedFooter.tsx

# Replace YouTube icon
sed -i 's/<span className="text-white font-bold text-sm">yt<\/span>/<Youtube className="w-5 h-5 text-white" \/>/' components/layout/EnhancedFooter.tsx

# Replace Twitter icon
sed -i 's/<span className="text-white font-bold text-sm">tw<\/span>/<Twitter className="w-5 h-5 text-white" \/>/' components/layout/EnhancedFooter.tsx

# Update social media URLs
sed -i 's/href="#"/href="https:\/\/facebook.com\/nextgenyouthmovement"/' components/layout/EnhancedFooter.tsx
sed -i 's/href="#" className="w-10 h-10 bg-hot-pink/href="https:\/\/instagram.com\/nextgenyouthmovement" className="w-10 h-10 bg-hot-pink/' components/layout/EnhancedFooter.tsx
sed -i 's/href="#" className="w-10 h-10 bg-bright-orange/href="https:\/\/youtube.com\/@nextgenyouthmovement" className="w-10 h-10 bg-bright-orange/' components/layout/EnhancedFooter.tsx
sed -i 's/href="#" className="w-10 h-10 bg-neon-green/href="https:\/\/twitter.com\/nextgenyouth" className="w-10 h-10 bg-neon-green/' components/layout/EnhancedFooter.tsx

# Add hover effects
sed -i 's/hover:scale-110 transition-transform duration-300/hover:scale-110 hover:shadow-lg hover:shadow-electric-blue\/30 transition-all duration-300/' components/layout/EnhancedFooter.tsx

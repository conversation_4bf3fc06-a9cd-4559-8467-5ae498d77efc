'use client';

import { Target, <PERSON>, Heart, Sparkles } from 'lucide-react';

const AboutSection = () => {
  const features = [
    {
      icon: <Target className="w-8 h-8" />,
      title: "Purpose-Driven Leadership",
      description: "Discover your unique calling and develop the skills to lead with purpose and integrity.",
      color: "electric-blue",
      hoverClass: "card-electric-blue"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Community Impact",
      description: "Make a real difference in your community through hands-on service projects and initiatives.",
      color: "neon-green",
      hoverClass: "card-neon-green"
    },
    {
      icon: <Heart className="w-8 h-8" />,
      title: "Mentorship & Growth",
      description: "Connect with experienced mentors who will guide your personal and spiritual development.",
      color: "bright-orange",
      hoverClass: "card-bright-orange"
    },
    {
      icon: <Sparkles className="w-8 h-8" />,
      title: "Transformative Programs",
      description: "Participate in life-changing workshops, conferences, and leadership development programs.",
      color: "hot-pink",
      hoverClass: "card-hot-pink"
    }
  ];

  return (
    <section id="about" className="features-section py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-arvo font-bold text-foreground mb-6">
            Why Choose <span className="bg-gradient-to-r from-electric-blue via-neon-green to-bright-orange bg-clip-text text-transparent animate-gradient-x">NextGen</span>?
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            We're more than just a youth program. We're a movement dedicated to unlocking 
            the incredible potential within every young person.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div
              key={feature.title}
              className={`bg-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-6 text-center card-hover-effect group ${feature.hoverClass}`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-${feature.color} to-${feature.color}/70 text-white mb-4 group-hover:scale-110 transition-transform duration-300`}>
                {feature.icon}
              </div>
              
              <h3 className="text-xl font-arvo font-bold text-foreground mb-3">
                {feature.title}
              </h3>
              
              <p className="text-muted-foreground text-sm leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default AboutSection;

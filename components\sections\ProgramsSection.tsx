'use client';

import { Calendar, Users, Award, BookOpen } from 'lucide-react';

const ProgramsSection = () => {
  const programs = [
    {
      icon: <Calendar className="w-8 h-8" />,
      title: "Leadership Workshops",
      description: "Monthly intensive workshops focusing on leadership skills, communication, and team building.",
      duration: "Monthly",
      participants: "15-20",
      color: "electric-blue",
      hoverClass: "card-electric-blue"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Community Projects",
      description: "Hands-on service projects that make a real impact in local communities.",
      duration: "Ongoing",
      participants: "25-30",
      color: "neon-green",
      hoverClass: "card-neon-green"
    },
    {
      icon: <Award className="w-8 h-8" />,
      title: "Mentorship Program",
      description: "One-on-one mentoring relationships with experienced leaders and professionals.",
      duration: "6 Months",
      participants: "1-on-1",
      color: "bright-orange",
      hoverClass: "card-bright-orange"
    },
    {
      icon: <BookOpen className="w-8 h-8" />,
      title: "Youth Conferences",
      description: "Annual conferences featuring inspiring speakers, workshops, and networking opportunities.",
      duration: "Annual",
      participants: "200+",
      color: "hot-pink",
      hoverClass: "card-hot-pink"
    }
  ];

  return (
    <section id="programs" className="features-section py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-arvo font-bold text-foreground mb-6">
            Our <span className="bg-gradient-to-r from-electric-blue via-neon-green to-bright-orange bg-clip-text text-transparent animate-gradient-x">Programs</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Discover transformative programs designed to develop leadership skills, 
            build character, and create lasting impact in your community.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {programs.map((program, index) => (
            <div
              key={program.title}
              className={`bg-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-8 card-hover-effect group ${program.hoverClass}`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-start space-x-4">
                <div className={`flex-shrink-0 w-16 h-16 rounded-full bg-gradient-to-r from-${program.color} to-${program.color}/70 text-white flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                  {program.icon}
                </div>
                
                <div className="flex-1">
                  <h3 className="text-xl font-arvo font-bold text-foreground mb-3">
                    {program.title}
                  </h3>
                  
                  <p className="text-muted-foreground text-sm leading-relaxed mb-4">
                    {program.description}
                  </p>
                  
                  <div className="flex space-x-4 text-xs text-muted-foreground">
                    <span className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>{program.duration}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <Users className="w-3 h-3" />
                      <span>{program.participants}</span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <a href="#contact" className="btn-enhanced inline-flex items-center space-x-2">
            <span>Join Our Programs</span>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </a>
        </div>
      </div>
    </section>
  );
};

export default ProgramsSection;

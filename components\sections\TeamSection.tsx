'use client';

import { motion } from 'framer-motion';
import { Users, ArrowRight } from 'lucide-react';
import TeamMemberCard from '../ui/TeamMemberCard';

const TeamSection = () => {
  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'Executive Director',
      bio: '<PERSON> has over 15 years of experience in youth development and nonprofit leadership.',
      image: '/api/placeholder/300/300',
      socialLinks: [
        { platform: 'linkedin', url: '#' },
        { platform: 'email', url: 'mailto:<EMAIL>' },
      ],
      color: 'electric-blue',
      achievements: [
        'Led organization to 300% growth in program participation',
        'Recognized as Youth Advocate of the Year 2023',
        'Masters in Nonprofit Management from State University',
      ],
    },
    {
      name: '<PERSON>',
      role: 'Program Director',
      bio: '<PERSON> brings a passion for youth empowerment and innovative program development.',
      image: '/api/placeholder/300/300',
      socialLinks: [
        { platform: 'linkedin', url: '#' },
        { platform: 'twitter', url: '#' },
      ],
      color: 'neon-green',
      achievements: [
        'Developed award-winning leadership curriculum',
        'Former youth pastor with 10+ years experience',
        'Certified in Youth Development and Mentoring',
      ],
    },
    {
      name: '<PERSON>',
      role: 'Community Outreach Manager',
      bio: '<PERSON> specializes in building partnerships and expanding our community reach.',
      image: '/api/placeholder/300/300',
      socialLinks: [
        { platform: 'linkedin', url: '#' },
        { platform: 'instagram', url: '#' },
      ],
      color: 'bright-orange',
      achievements: [
        'Established partnerships with 50+ local organizations',
        'Increased community engagement by 200%',
        'Background in Social Work and Community Development',
      ],
    }
  ];

  return (
    <section id="team" className="py-20 features-section">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-arvo font-bold text-foreground mb-6">
            Meet Our <span className="gradient-text">Team</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Our dedicated team of leaders, mentors, and volunteers are passionate about 
            empowering the next generation to discover their potential.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {teamMembers.map((member, index) => (
            <motion.div
              key={member.name}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <TeamMemberCard {...member} />
            </motion.div>
          ))}
        </div>

        <div className="text-center mt-12">
          <a
            href="#contact"
            className="btn-enhanced inline-flex items-center space-x-2"
          >
            <Users size={20} />
            <span>Join Our Team</span>
            <ArrowRight size={20} />
          </a>
        </div>
      </div>
    </section>
  );
};

export default TeamSection;

'use client';

import { useEffect, useRef } from 'react';
import Link from 'next/link';
import { ArrowR<PERSON>, Sparkles } from 'lucide-react';
import gsapUtils from '../../lib/gsap-utils';

const HeroSection = () => {
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // GSAP Hero Animation Sequence
    if (titleRef.current && subtitleRef.current && ctaRef.current) {
      gsapUtils.heroSequence({
        title: titleRef.current,
        subtitle: subtitleRef.current,
        cta: ctaRef.current
      });
    }

    // Floating particles animation
    gsapUtils.createParticles('.hero-particles', 15);
  }, []);

  return (
    <section 
      id="home"
      ref={heroRef}
      className="hero-section relative min-h-screen flex items-center justify-center overflow-hidden"
    >
      {/* Animated Background Particles */}
      <div className="hero-particles absolute inset-0"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Main Headline */}
        <h1
          ref={titleRef}
          className="text-5xl md:text-7xl font-arvo font-bold text-foreground leading-tight mb-8"
        >
          Empowering{' '}
          <span className="bg-gradient-to-r from-electric-blue via-neon-green to-bright-orange bg-clip-text text-transparent animate-gradient-x">
            Tomorrow's
          </span>{' '}
          Leaders
        </h1>

        {/* Subtitle */}
        <p
          ref={subtitleRef}
          className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto mb-12 leading-relaxed font-arvo"
        >
          Discover your God-given potential through transformative leadership programs, 
          meaningful mentorship, and impactful community engagement.
        </p>

        {/* CTA Buttons */}
        <div
          ref={ctaRef}
          className="flex flex-col sm:flex-row gap-6 justify-center items-center"
        >
          <a href="#programs" className="btn-enhanced group inline-flex items-center space-x-2">
            <Sparkles size={24} />
            <span>Explore Programs</span>
            <ArrowRight size={24} className="group-hover:translate-x-1 transition-transform" />
          </a>

          <a 
            href="#about" 
            className="px-8 py-4 border-2 border-primary text-primary font-semibold rounded-lg hover:bg-primary hover:text-primary-foreground transition-all duration-300 font-arvo"
          >
            Learn Our Story
          </a>
        </div>
      </div>

      
    </section>
  );
};

export default HeroSection;
